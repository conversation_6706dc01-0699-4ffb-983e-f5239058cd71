using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace FluentDesignWPF.Controls.Basic
{
    /// <summary>
    /// FluentLabel 控件，基于 Microsoft Fluent Design System 的标签控件
    /// 继承自 Label，提供了丰富的样式变体和主题支持
    /// </summary>
    /// <remarks>
    /// FluentLabel 提供以下特性：
    /// 1. 完整的 Typography 层次结构支持（Display、Headline、Title、Body、Caption）
    /// 2. 语义化颜色变体（Primary、Secondary、Success、Warning、Error、Info）
    /// 3. 多种字重支持（Light、Regular、Medium、SemiBold、Bold）
    /// 4. 深色/浅色主题自动适配
    /// 5. WCAG 可访问性合规
    /// 6. 响应式设计支持
    /// </remarks>
    public class FluentLabel : Label
    {
        #region 静态构造函数

        /// <summary>
        /// 静态构造函数，注册默认样式
        /// </summary>
        static FluentLabel()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(FluentLabel), 
                new FrameworkPropertyMetadata(typeof(FluentLabel)));
        }

        #endregion

        #region 依赖属性

        /// <summary>
        /// Typography 变体依赖属性
        /// </summary>
        public static readonly DependencyProperty TypographyVariantProperty =
            DependencyProperty.Register(
                nameof(TypographyVariant),
                typeof(TypographyVariant),
                typeof(FluentLabel),
                new FrameworkPropertyMetadata(
                    TypographyVariant.Body2,
                    FrameworkPropertyMetadataOptions.AffectsRender | FrameworkPropertyMetadataOptions.AffectsMeasure,
                    OnTypographyVariantChanged));

        /// <summary>
        /// 颜色变体依赖属性
        /// </summary>
        public static readonly DependencyProperty ColorVariantProperty =
            DependencyProperty.Register(
                nameof(ColorVariant),
                typeof(ColorVariant),
                typeof(FluentLabel),
                new FrameworkPropertyMetadata(
                    ColorVariant.Primary,
                    FrameworkPropertyMetadataOptions.AffectsRender,
                    OnColorVariantChanged));

        /// <summary>
        /// 字重变体依赖属性
        /// </summary>
        public static readonly DependencyProperty FontWeightVariantProperty =
            DependencyProperty.Register(
                nameof(FontWeightVariant),
                typeof(FontWeightVariant),
                typeof(FluentLabel),
                new FrameworkPropertyMetadata(
                    FontWeightVariant.Regular,
                    FrameworkPropertyMetadataOptions.AffectsRender,
                    OnFontWeightVariantChanged));

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取或设置 Typography 变体
        /// </summary>
        /// <value>Typography 变体枚举值，默认为 Body2</value>
        public TypographyVariant TypographyVariant
        {
            get => (TypographyVariant)GetValue(TypographyVariantProperty);
            set => SetValue(TypographyVariantProperty, value);
        }

        /// <summary>
        /// 获取或设置颜色变体
        /// </summary>
        /// <value>颜色变体枚举值，默认为 Primary</value>
        public ColorVariant ColorVariant
        {
            get => (ColorVariant)GetValue(ColorVariantProperty);
            set => SetValue(ColorVariantProperty, value);
        }

        /// <summary>
        /// 获取或设置字重变体
        /// </summary>
        /// <value>字重变体枚举值，默认为 Regular</value>
        public FontWeightVariant FontWeightVariant
        {
            get => (FontWeightVariant)GetValue(FontWeightVariantProperty);
            set => SetValue(FontWeightVariantProperty, value);
        }

        #endregion

        #region 属性变更回调

        /// <summary>
        /// Typography 变体属性变更回调
        /// </summary>
        /// <param name="d">依赖对象</param>
        /// <param name="e">属性变更事件参数</param>
        private static void OnTypographyVariantChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FluentLabel label)
            {
                label.ApplyTypographyVariant();
            }
        }

        /// <summary>
        /// 颜色变体属性变更回调
        /// </summary>
        /// <param name="d">依赖对象</param>
        /// <param name="e">属性变更事件参数</param>
        private static void OnColorVariantChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FluentLabel label)
            {
                label.ApplyColorVariant();
            }
        }

        /// <summary>
        /// 字重变体属性变更回调
        /// </summary>
        /// <param name="d">依赖对象</param>
        /// <param name="e">属性变更事件参数</param>
        private static void OnFontWeightVariantChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FluentLabel label)
            {
                label.ApplyFontWeightVariant();
            }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 FluentLabel 类的新实例
        /// </summary>
        public FluentLabel()
        {
            // 设置默认属性
            UseLayoutRounding = true;

            // 当控件加载到可视化树时应用样式
            Loaded += OnLoaded;
        }

        /// <summary>
        /// 控件加载事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 应用初始样式
            ApplyTypographyVariant();
            ApplyColorVariant();
            ApplyFontWeightVariant();

            // 只需要在第一次加载时处理
            Loaded -= OnLoaded;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 应用 Typography 变体样式
        /// </summary>
        private void ApplyTypographyVariant()
        {
            try
            {
                var resourceKey = $"{TypographyVariant}TextStyle";
                if (TryFindResource(resourceKey) is Style style)
                {
                    // 合并样式而不是完全替换
                    MergeStyle(style);
                }
            }
            catch (Exception ex)
            {
                // 在设计时或资源未加载时可能会出现异常，记录但不抛出
                System.Diagnostics.Debug.WriteLine($"Failed to apply typography variant {TypographyVariant}: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用颜色变体样式
        /// </summary>
        private void ApplyColorVariant()
        {
            try
            {
                var brushKey = GetColorBrushKey(ColorVariant);
                if (TryFindResource(brushKey) is Brush brush)
                {
                    Foreground = brush;
                }
            }
            catch (Exception ex)
            {
                // 在设计时或资源未加载时可能会出现异常，记录但不抛出
                System.Diagnostics.Debug.WriteLine($"Failed to apply color variant {ColorVariant}: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用字重变体样式
        /// </summary>
        private void ApplyFontWeightVariant()
        {
            try
            {
                FontWeight = GetFontWeight(FontWeightVariant);
            }
            catch (Exception ex)
            {
                // 在设计时或资源未加载时可能会出现异常，记录但不抛出
                System.Diagnostics.Debug.WriteLine($"Failed to apply font weight variant {FontWeightVariant}: {ex.Message}");
            }
        }

        /// <summary>
        /// 合并样式到当前控件
        /// </summary>
        /// <param name="style">要合并的样式</param>
        private void MergeStyle(Style style)
        {
            if (style?.Setters == null) return;

            foreach (var setter in style.Setters)
            {
                if (setter is Setter s)
                {
                    try
                    {
                        SetValue(s.Property, s.Value);
                    }
                    catch
                    {
                        // 忽略设置失败的属性
                    }
                }
            }
        }

        /// <summary>
        /// 获取颜色变体对应的画刷资源键
        /// </summary>
        /// <param name="colorVariant">颜色变体</param>
        /// <returns>画刷资源键</returns>
        private static string GetColorBrushKey(ColorVariant colorVariant)
        {
            return colorVariant switch
            {
                ColorVariant.Primary => "TextPrimaryBrush",
                ColorVariant.Secondary => "TextSecondaryBrush",
                ColorVariant.Disabled => "TextDisabledBrush",
                ColorVariant.Hint => "TextHintBrush",
                ColorVariant.Success => "SuccessBrush",
                ColorVariant.Warning => "WarningBrush",
                ColorVariant.Error => "ErrorBrush",
                ColorVariant.Info => "InfoBrush",
                ColorVariant.Accent => "AccentBrush",
                _ => "TextPrimaryBrush"
            };
        }

        /// <summary>
        /// 获取字重变体对应的 FontWeight
        /// </summary>
        /// <param name="fontWeightVariant">字重变体</param>
        /// <returns>FontWeight 值</returns>
        private static FontWeight GetFontWeight(FontWeightVariant fontWeightVariant)
        {
            return fontWeightVariant switch
            {
                FontWeightVariant.Light => FontWeights.Light,
                FontWeightVariant.Regular => FontWeights.Normal,
                FontWeightVariant.Medium => FontWeights.Medium,
                FontWeightVariant.SemiBold => FontWeights.SemiBold,
                FontWeightVariant.Bold => FontWeights.Bold,
                _ => FontWeights.Normal
            };
        }

        #endregion
    }
}
